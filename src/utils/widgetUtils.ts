import { ColorUtils } from './colorUtils';
import { widgetSettings } from '../config/widgetSettings';
import { TextNode } from '../types/figma';

export class WidgetUtils {
  public static processWidgetSettings(node: any, data: any) {
    if (!Array.isArray(data.settings)) {
      // Process basic settings
      if (node.type === "TEXT") {
        data.settings.text = node.characters;
      }

      // Process layout settings
      if (node.layoutMode) {
        data.settings.flex_direction = node.layoutMode === 'HORIZONTAL' ? 'row' : 'column';
      }

      // Process alignment
      if (node.primaryAxisAlignItems) {
        const alignment = node.primaryAxisAlignItems;
        if (alignment === 'SPACE_BETWEEN') {
          data.settings.flex_justify_content = 'space-between';
        } else if (alignment === 'CENTER') {
          data.settings.flex_justify_content = 'center';
        } else if (alignment === 'MIN') {
          data.settings.flex_justify_content = 'flex-start';
        } else if (alignment === 'MAX') {
          data.settings.flex_justify_content = 'flex-end';
        }
      }
    }
  }

  // Get typography settings only without mapping with widgets
  public static getTypographySettings(node: any, data: any) {
    let typography = {
      typography: '',
      fontFamily: '',
      fontWeight: '',
      fontSize: {},
      lineHeight: {},
      textTransform: '',
      letterSpacing: {},
      textDecoration: '',
    };

    if (!Array.isArray(data.settings)) {
      typography = {
        typography: 'custom',
        fontFamily: node.fontName?.family,
        fontWeight: node.fontName?.style === "Regular" ? "400"
          : node.fontName?.style === "Medium" ? "500"
            : node.fontName?.style === "SemiBold" ? "600"
              : node.fontName?.style === "Bold" ? "700"
                : node.fontName?.style === "ExtraBold" ? "800"
                  : node.fontName?.style === "Black" ? "900"
                    : node.fontName?.style,
        fontSize: node.fontSize ? { unit: "px", size: node.fontSize, sizes: [] } : undefined,
        lineHeight: node.lineHeight ? { unit: node.lineHeight.unit === "PERCENT" ? "%" : "px", size: node.lineHeight.value, sizes: [] } : undefined,
        textTransform: node.textCase?.toLowerCase() || 'none',
        letterSpacing: node.letterSpacing ? { unit: node.letterSpacing.unit, size: node.letterSpacing.value, sizes: [] } : undefined,
        textDecoration: node.textDecoration?.toLowerCase() || 'none',
      };

      // if original then, change to none;
      typography.textTransform = (node.textCase === "ORIGINAL" || node.textCase === 'TITLE') ? 'none' : typography.textTransform;
    }

    return typography;
  }

  public static processTypographySettings(node: TextNode, data: any, widgetNodeType: string): void {
    if (!node || !data || Array.isArray(data.settings)) {
      return;
    }

    const typography = WidgetUtils.getTypographySettings(node, data);

    if (data.widgetType && widgetNodeType && widgetSettings[data.widgetType]?.typography?.[widgetNodeType]) {
      const prefix = widgetSettings[data.widgetType].typography![widgetNodeType];
      data.settings[`${prefix}_typography`] = typography.typography;
      data.settings[`${prefix}_font_family`] = typography.fontFamily;
      data.settings[`${prefix}_text_decoration`] = typography.textDecoration;

      // Special case for Advanced Menu - ensure it gets the correct font weight
      if (data.widgetType === 'eael-advanced-menu' && widgetNodeType === 'eael-advanced-menu-item') {
        data.settings[`${prefix}_font_weight`] = "400"; // Force 400 (Regular) for Advanced Menu
      } else {
        data.settings[`${prefix}_font_weight`] = typography.fontWeight;
      }

      if (typography.fontSize) data.settings[`${prefix}_font_size`] = typography.fontSize;
      // Skip line height for advanced menu and cta box
      if (typography.lineHeight 
          && !(data.widgetType === 'eael-advanced-menu' && widgetNodeType === 'eael-advanced-menu-item') 
          && !(widgetNodeType === 'eael-cta-box-button')
          && !(widgetNodeType === 'eael-cta-box-button-secondary')
        ) {
        data.settings[`${prefix}_line_height`] = typography.lineHeight;
      }
      if (typography.textTransform) data.settings[`${prefix}_text_transform`] = typography.textTransform;
      if (typography.letterSpacing) data.settings[`${prefix}_letter_spacing`] = typography.letterSpacing;
    } else {
      data.settings.typography_typography = typography.typography;
      data.settings.typography_font_family = typography.fontFamily;
      data.settings.typography_font_weight = typography.fontWeight;
      if (typography.fontSize) data.settings.typography_font_size = typography.fontSize;
      if (typography.lineHeight) data.settings.typography_line_height = typography.lineHeight;
    }
  }

  public static getColorSettings(node: any, data: any) {
    if (node.type !== "TEXT" || !("fills" in node) || !Array.isArray(node.fills) || node.fills.length === 0) {
      return;
    }

    const fill = node.fills[0];
    if (!["SOLID", "GRADIENT_LINEAR"].includes(fill.type) || Array.isArray(data.settings)) {
      return;
    }

    const color = ColorUtils.rgbToHex(fill.color);

    return color;
  }

  /**
   * Get color fallback when no fill is defined on the node
   * Tries to extract colors from local paint styles, current selection, or sibling nodes
   */
  public static getSelectionColorFallback(node: any): string | undefined {
    try {
      // Method 1: Try to get colors from current selection
      const selection = figma.currentPage.selection;
      if (selection && selection.length > 0) {
        for (const selectedNode of selection) {
          if ('fills' in selectedNode && selectedNode.fills && Array.isArray(selectedNode.fills) && selectedNode.fills.length > 0) {
            const fill = selectedNode.fills[0];
            if (fill.type === 'SOLID' && fill.color) {
              return ColorUtils.rgbToHex(fill.color);
            }
          }
        }
      }

      // Method 2: Try to get colors from sibling nodes
      if (node.parent && 'children' in node.parent) {
        for (const sibling of node.parent.children) {
          if (sibling !== node && 'fills' in sibling && sibling.fills && Array.isArray(sibling.fills) && sibling.fills.length > 0) {
            const fill = sibling.fills[0];
            if (fill.type === 'SOLID' && fill.color) {
              return ColorUtils.rgbToHex(fill.color);
            }
          }
        }
      }

      return undefined;
    } catch (error) {
      // Silently fail and return undefined if any error occurs
      return undefined;
    }
  }

  /**
   * Get color from local paint styles asynchronously
   * This is a helper method for getting colors from Figma's local paint styles
   */
  public static async getLocalPaintStyleColor(): Promise<string | undefined> {
    try {
      const localPaintStyles = await figma.getLocalPaintStylesAsync();
      if (localPaintStyles && localPaintStyles.length > 0) {
        for (const paintStyle of localPaintStyles) {
          if (paintStyle.paints && paintStyle.paints.length > 0) {
            const paint = paintStyle.paints[0];
            if (paint.type === 'SOLID' && paint.color) {
              return ColorUtils.rgbToHex(paint.color);
            }
          }
        }
      }
      return undefined;
    } catch (error) {
      return undefined;
    }
  }

  public static processTextColor(node: any, data: any, widgetNodeType: string) {
    let color = WidgetUtils.getColorSettings(node, data);

    // If no color found from fills, try fallback methods
    if (!color) {
      color = WidgetUtils.getSelectionColorFallback(node);
    }

    if (!color) {
      return;
    }

    const widgetType = data.widgetType;

    if (widgetType && widgetNodeType && widgetSettings[widgetType]?.color?.[widgetNodeType]) {
      data.settings[widgetSettings[widgetType].color![widgetNodeType]] = color;

      // Handle special cases
      if (widgetType === "eael-cta-box" && widgetNodeType === "eael-cta-box-button") {
        data.settings.eael_cta_btn_hover_text_color = ""; // static for now
      }

      if (widgetNodeType === 'eael-cta-box-button-secondary') {
        data.settings.eael_cta_secondary_btn_hover_text_color = color;
      }

      if (widgetType === "eael-info-box" && widgetNodeType === "eael-info-box-button") {
        data.settings.eael_infobox_button_hover_text_color = color;
      }

      if (widgetNodeType === "eael-creative-button-text") {
        data.settings.eael_creative_button_hover_text_color = color;
        data.settings.eael_creative_button_icon_color = color;
        data.settings.eael_creative_button_hover_icon_color = color;
      }

      if (widgetNodeType === "eael-dch-first-title") {
        data.settings.eael_dch_dual_title_color = color;
      }

      if (widgetNodeType === "eael-advanced-menu-item") {
        data.settings.default_eael_advanced_menu_item_indicator_color = color;
        data.settings.default_eael_advanced_menu_dropdown_item_color = color;
        data.settings.default_eael_advanced_menu_dropdown_item_indicator_color = color;

        // hover
        data.settings.default_eael_advanced_menu_item_color_hover = color;
      }

      if (widgetNodeType === "eael-advanced-menu-item-hover") {
        data.settings.default_eael_advanced_menu_item_indicator_color_hover = color;
        data.settings.default_eael_advanced_menu_dropdown_item_color_hover = color;
        data.settings.default_eael_advanced_menu_dropdown_item_indicator_color_hover = color;
      }

      if (widgetNodeType === "eael-post-carousel-title") {
        data.settings.eael_post_grid_title_hover_color = color;
      }

      if (widgetNodeType === "eael-counter-number") {
        data.settings.section_number_suffix_color = color;
      }
    } else {
      data.settings.title_color = color; // text-editor: title_color
    }
  }

  public static processBackground(node: any, data: any, widgetNodeType?: string) {
    if (node.type === "TEXT" || !("fills" in node) || !Array.isArray(node.fills) || node.fills.length === 0) {
      if (widgetNodeType === "eael-info-box-button" && !Array.isArray(data.settings)) {
        data.settings.eael_infobox_button_background_color = "";
        data.settings.eael_infobox_button_hover_background_color = "";
      }
      return;
    }

    const fill = node.fills[0];
    if (Array.isArray(data.settings)) {
      return;
    }

    const backgroundType = fill.type === "SOLID" ? "classic" : "gradient";
    let backgroundColor = ColorUtils.rgbToHex(fill.color);
    const backgroundColorB = fill.type === "GRADIENT_LINEAR" ? ColorUtils.rgbToHex(fill.gradientStops[1]?.color) : "";

    if (!backgroundColor) {
      backgroundColor = backgroundColorB;
    }

    if (data.widgetType && widgetNodeType && widgetSettings[data.widgetType]?.background?.[widgetNodeType]) {
      data.settings[widgetSettings[data.widgetType].background![widgetNodeType]] = backgroundColor;

      // Handle special cases
      if (data.widgetType === "eael-info-box" && widgetNodeType === "eael-info-box-button") {
        data.settings.eael_infobox_button_background_color_color = backgroundColor;
        data.settings.eael_infobox_button_hover_background_color_color = backgroundColor;
        data.settings.eael_infobox_button_hover_background_color = backgroundColor;

        if (backgroundColorB) {
          data.settings.eael_infobox_button_background_color_color_b = backgroundColorB;
          data.settings.eael_infobox_button_hover_background_color_color_b = backgroundColorB;
          data.settings.eael_infobox_button_background_color_b = backgroundColorB;
          data.settings.eael_infobox_button_hover_background_color_b = backgroundColorB;
        }
      } else if (widgetNodeType === "eael-creative-button-container") {
        data.settings.eael_creative_button_hover_background_color = backgroundColor;
      }

      if (widgetNodeType === "eael-cta-box-button") {
        data.settings.eael_cta_btn_hover_bg_color = backgroundColor;
      }

      if (widgetNodeType === "eael-cta-box-button-secondary") {
        data.settings.eael_cta_secondary_btn_normal_bg_color_background = 'classic';
        data.settings.eael_cta_secondary_btn_hover_bg_color_background = 'classic';
        data.settings.eael_cta_secondary_btn_hover_bg_color_color = backgroundColor;
      }

      if (widgetNodeType === "eael-advanced-menu-container") {
        data.settings.default_eael_advanced_menu_item_background_hover = backgroundColor;
      }

      if (widgetNodeType === "eael-feature-list-icon") {
        data.settings.eael_feature_list_icon_background_background = 'classic';
      }

      if (widgetNodeType === "eael-counter-container") {
        data.settings._background_background = 'classic';
      }

      if (widgetNodeType === "eael-woo-product-list-container") {
        data.settings.eael_product_list_container_normal_background_background = 'classic';
      }

      if (widgetNodeType === "eael-post-carousel-container") {
        data.settings.eael_section_post_grid_container_background_background = backgroundType;
        data.settings.eael_section_post_grid_container_background_color = backgroundColor;
        if (backgroundColorB) {
          data.settings.eael_section_post_grid_container_background_color_b = backgroundColorB;
        }
      }

      if (widgetNodeType === "eael-post-carousel-dot") {
        data.settings.eael_post_grid_pagination_dot_color_background = backgroundType;
        data.settings.eael_post_grid_pagination_dot_color_color = backgroundColor;
        if (backgroundColorB) {
          data.settings.eael_post_grid_pagination_dot_color_color_b = backgroundColorB;
        }

        data.settings.dots_color_normal = backgroundColor;
        data.settings.dots_border_normal_color = backgroundColor;
      }

      if (widgetNodeType === "eael-post-carousel-active-dot") {
        data.settings.eael_post_grid_pagination_dot_active_color_background = backgroundType;
        data.settings.eael_post_grid_pagination_dot_active_color_color = backgroundColor;
        if (backgroundColorB) {
          data.settings.eael_post_grid_pagination_dot_active_color_color_b = backgroundColorB;
        }

        data.settings.active_dot_color_normal = backgroundColor;
      }
    } else {
      data.settings.background_background = backgroundType;
      data.settings.background_color = backgroundColor;
      if (backgroundColorB) {
        data.settings.background_color_b = backgroundColorB;
      }
    }
  }

  public static processBorderRadius(node: any, data: any, widgetNodeType?: string) {
    if (!("cornerRadius" in node) || node.cornerRadius === 0) return;
    if (Array.isArray(data.settings)) return;

    const cornerRadius = node.cornerRadius ?? 0;

    let border_radius = {
      unit: "px",
      top: cornerRadius.toString(),
      right: cornerRadius.toString(),
      bottom: cornerRadius.toString(),
      left: cornerRadius.toString(),
      isLinked: true
    };

    let border_radius_v2 = {
      unit: "px",
      size: cornerRadius.toString(),
      sizes: [],
    };

    // Set the border radius based on widget type and node type
    if (data.widgetType && widgetNodeType && widgetSettings[data.widgetType]?.borderRadius?.[widgetNodeType]) {
      // Special cases that need v2 format
      if (
        (data.widgetType === 'eael-cta-box' && widgetNodeType === 'eael-cta-box-button') ||
        (data.widgetType === 'eael-cta-box' && widgetNodeType === 'eael-cta-box-container') ||
        (data.widgetType === 'eael-info-box' && widgetNodeType === 'eael-info-box-button') ||
        (data.widgetType === 'eael-creative-button' && widgetNodeType === 'eael-creative-button-container') ||
        (data.widgetType === 'eael-post-carousel' && widgetNodeType === 'eael-post-carousel-dot') ||
        (data.widgetType === 'eael-post-carousel' && widgetNodeType === 'eael-post-carousel-active-dot')
      ) {
        data.settings[widgetSettings[data.widgetType].borderRadius![widgetNodeType]] = border_radius_v2;

        if (widgetNodeType === 'eael-post-carousel-active-dot') {
          data.settings.active_dots_radius = border_radius;
        }
      }
      // Special case for info box image
      else if (data.widgetType === 'eael-info-box' && widgetNodeType === 'eael-info-box-image') {
        data.settings.eael_infobox_img_shape = 'radius';
        data.settings[widgetSettings[data.widgetType].borderRadius![widgetNodeType]] = border_radius;
      }
      // Default case
      else {
        data.settings[widgetSettings[data.widgetType].borderRadius![widgetNodeType]] = border_radius;
      }
    } else {
      data.settings.border_radius = border_radius;
    }
  }

  public static processBorderWidth(node: any, data: any, widgetNodeType?: string) {
    if (!("strokeWeight" in node) || !node.strokeWeight) {
      return;
    }

    if (Array.isArray(data.settings)) {
      return;
    }

    // Check if strokes are actually visible in the Figma design
    // Only apply border if strokes exist, are visible, and have non-zero opacity
    const hasVisibleStrokes = node.strokes &&
                             node.strokes.length > 0 &&
                             (node.strokes[0].visible !== false) &&
                             (node.strokes[0].opacity !== 0);

    if (!hasVisibleStrokes) {
      return;
    }

    const borderWidth = {
      unit: "px",
      top: node.strokeWeight.toString(),
      right: node.strokeWeight.toString(),
      bottom: node.strokeWeight.toString(),
      left: node.strokeWeight.toString(),
      isLinked: true
    };

    // Map widget types to their border width property names


    // Set the border width based on widget type and node type
    if (data.widgetType && widgetNodeType && widgetSettings[data.widgetType]?.borderWidth?.[widgetNodeType]) {
      data.settings[widgetSettings[data.widgetType].borderWidth![widgetNodeType]] = borderWidth;

      // Set border style
      if (data.widgetType === 'eael-cta-box') {
        if (widgetNodeType === 'eael-cta-box-button') {
          data.settings.eael_cat_btn_normal_border_border = 'solid';
        } else {
          data.settings.eael_cta_border_border = 'solid';
        }
      } else if (data.widgetType === 'eael-info-box' && widgetNodeType === 'eael-info-box-button') {
        data.settings.eael_infobox_button_border_border = 'solid';

        // Set border color if available
        if (node.strokes && node.strokes.length > 0 && 'color' in node.strokes[0]) {
          data.settings.eael_infobox_button_border_color = ColorUtils.rgbToHex(node.strokes[0].color);
        }
      }
    } else {
      data.settings.border_width = borderWidth;
      data.settings.border_border = 'solid';
      data.settings.border_color = ColorUtils.rgbToHex(node.strokes[0].color);
    }
  }

  public static processBoxShadow(node: any, data: any, widgetNodeType?: string) {
    if (Array.isArray(data.settings)) {
      return;
    }

    // if no effects, then set default box shadow to zero values for cta box buttons
    if (!("effects" in node) || !Array.isArray(node.effects) || node.effects.length === 0) {
      if ( widgetNodeType == 'eael-cta-box-button' || widgetNodeType == 'eael-cta-box-button-secondary' ) {
        data.settings[widgetSettings[data.widgetType].boxShadow![widgetNodeType] + '_box_shadow_type'] = 'yes';
        // default box shadow with all zero values
        data.settings[widgetSettings[data.widgetType].boxShadow![widgetNodeType] + '_box_shadow'] = {
          horizontal: 0,
          vertical: 0,
          blur: 0,
          spread: 0,
          color: 'rgba(0,0,0,0.5)',
          // position: 'outline'
        };
      }
      return;
    }

    // Find the first visible drop shadow effect
    const dropShadow = node.effects.find((effect: any) =>
      effect.type === 'DROP_SHADOW' &&
      effect.visible !== false
    );

    if (!dropShadow) {
      return;
    }

    // Extract shadow properties
    const offsetX = dropShadow.offset?.x || 0;
    const offsetY = dropShadow.offset?.y || 0;
    const blur = dropShadow.radius || 0;
    const spread = dropShadow.spread || 0;
    const shadowColor = dropShadow.color ? ColorUtils.rgbToHex(dropShadow.color) : 'rgba(0,0,0,0.5)';

    // Create box shadow object
    const boxShadow = {
      horizontal: offsetX,
      vertical: offsetY,
      blur: blur,
      spread: spread,
      color: shadowColor,
      // position: 'outline'
    };

    // Set the box shadow based on widget type and node type
    if (data.widgetType && widgetNodeType && widgetSettings[data.widgetType]?.boxShadow?.[widgetNodeType]) {
      data.settings[widgetSettings[data.widgetType].boxShadow![widgetNodeType] + '_box_shadow'] = boxShadow;
      data.settings[widgetSettings[data.widgetType].boxShadow![widgetNodeType] + '_box_shadow_type'] = 'yes'; // eael_cta_secondary_button_shadow_box_shadow_type = 'yes'
    } else {
      data.settings.box_shadow = boxShadow;
    }
  }

  public static processPadding(node: any, data: any, widgetNodeType?: string) {
    if (!("paddingLeft" in node) && !("paddingRight" in node) &&
      !("paddingTop" in node) && !("paddingBottom" in node)) {
      return;
    }

    if (Array.isArray(data.settings)) {
      return;
    }

    // Extract padding values, defaulting to 0 if not present
    const paddingLeft = "paddingLeft" in node ? node.paddingLeft : 0;
    const paddingRight = "paddingRight" in node ? node.paddingRight : 0;
    const paddingTop = "paddingTop" in node ? node.paddingTop : 0;
    const paddingBottom = "paddingBottom" in node ? node.paddingBottom : 0;

    // Check if all paddings are the same
    const isLinked = paddingLeft === paddingRight &&
      paddingRight === paddingTop &&
      paddingTop === paddingBottom;

    const padding = {
      unit: "px",
      top: paddingTop?.toString(),
      right: paddingRight?.toString(),
      bottom: paddingBottom?.toString(),
      left: paddingLeft?.toString(),
      isLinked: isLinked
    };

    // Handle different widget types
    if (data.widgetType && widgetNodeType && widgetSettings[data.widgetType]?.padding?.[widgetNodeType]) {
      data.settings[widgetSettings[data.widgetType].padding![widgetNodeType]] = padding;
    } else {
      // Default padding property for elements without specific widget mapping
      data.settings.padding = padding;
      if (data.widgetType === 'eael-counter') {
        data.settings._padding = padding;
      }
    }
  }

  /**
   * Determines the relative position of two nodes (above/below)
   * @param firstNode The first node to compare
   * @param secondNode The second node to compare
   * @returns 'top' if firstNode is above secondNode, 'default' otherwise
   */
  public static determineRelativePosition(firstNode: any, secondNode: any): 'top' | 'default' {
    // Check if nodes have absoluteBoundingBox
    if ('absoluteBoundingBox' in firstNode && 'absoluteBoundingBox' in secondNode) {
      const firstBoundingBox = firstNode.absoluteBoundingBox as { y: number };
      const secondBoundingBox = secondNode.absoluteBoundingBox as { y: number };

      // If first node's Y position is less than second node's Y position, it's at the top
      if (firstBoundingBox.y < secondBoundingBox.y) {
        return 'top';
      }
    }
    // Alternative check if absoluteBoundingBox is not available
    else if ('y' in firstNode && 'y' in secondNode) {
      const firstY = (firstNode as unknown as { y: number }).y;
      const secondY = (secondNode as unknown as { y: number }).y;

      if (firstY < secondY) {
        return 'top';
      }
    }

    // Default fallback
    return 'default';
  }

  public static processMargin(node: any, data: any, widgetNodeType?: string) {
    // Check if node has itemSpacing property (auto layout property)
    if (!(node.type == "TEXT" || node.name.startsWith('image')) && !("itemSpacing" in node)) return;

    // Skip if settings is an array
    if (Array.isArray(data.settings)) return;

    let itemSpacing = node.itemSpacing || 0;

    // Create margin object based on layout direction
    // In Figma, item spacing applies between items based on the layout direction
    let isHorizontal = "layoutMode" in node && node.layoutMode === "HORIZONTAL";

    let getParent = node.type === "TEXT" || node.name.startsWith('image') ? 1 : 0; // InfoBox: if image (first) and main-content, then need to fetch parent to get image bottom margin

    if (getParent) {
      const parent = node.parent;
      if (parent || ("itemSpacing" in parent)) {
        itemSpacing = parent.itemSpacing || 0;
        isHorizontal = "layoutMode" in parent && parent.layoutMode === "HORIZONTAL";
      }
    }

    // if horizontal and not last child, then add itemSpacing to right margin
    const isLastChild = node.parent?.children.indexOf(node) === node.parent?.children.length - 1;

    // if horizontal and not last child, then add itemSpacing to right margin
    let margin = {
      unit: "px",
      top: isHorizontal ? "0" : "0",
      right: isHorizontal && !isLastChild ? itemSpacing.toString() : "0",
      bottom: isHorizontal ? "0" : itemSpacing.toString(),
      left: isHorizontal ? "0" : "0",
      isLinked: false
    };

    // Set the margin based on widget type and node type
      if (data.widgetType && widgetNodeType && widgetSettings[data.widgetType]?.margin?.[widgetNodeType]) {
      if ('eael-info-box-main-content' == widgetNodeType) {
        // margin will be assigned to direct childrens.
        node.children.forEach((child: any) => {
          if (!Array.isArray(data.settings)) {
            if (child.name === "title") {
              data.settings.eael_infobox_title_margin = margin;
            } else if (child.name === "subtitle") {
              data.settings.eael_infobox_subtitle_margin = margin;
            } else if (child.name === "content") {
              data.settings.eael_infobox_content_margin = margin;
            } else if (child.name === "button") {
              data.settings.eael_infobox_button_margin = margin;
            }
          }
        });
      }
      else if ('eael-dch-container' == widgetNodeType || 'eael-cta-box-container' == widgetNodeType) {
        margin.right = '0';
        margin.bottom = '0';
        data.settings[widgetSettings[data.widgetType].margin![widgetNodeType]] = margin;
      }
      else if ('eael-feature-list-icon' == widgetNodeType) {
        data.settings[widgetSettings[data.widgetType].margin![widgetNodeType]] = {
          unit: 'px',
          size: isHorizontal ? margin.right : margin.bottom,
          sizes: []
        };
      }
      else if ('eael-feature-list-list' == widgetNodeType) {
        data.settings[widgetSettings[data.widgetType].margin![widgetNodeType]] = {
          unit: 'px',
          size: isHorizontal ? margin.right : margin.bottom,
          sizes: []
        };

        data.settings.eael_feature_list_title_bottom_space = {
          unit: 'px',
          size: isHorizontal ? margin.right : margin.bottom,
          sizes: []
        };
      }
      else {
        data.settings[widgetSettings[data.widgetType].margin![widgetNodeType]] = margin;
      }
    } else {
      data.settings.margin = margin;
    }
  }

  public static calculateSpacingBetweenNodes(node1: any, node2: any): number | null {
    if (!node1 || !node2) return null;

    // Get the absolute positions of both nodes
    const pos1 = {
      x: node1.absoluteBoundingBox?.x || node1.x,
      y: node1.absoluteBoundingBox?.y || node1.y,
      width: node1.absoluteBoundingBox?.width || node1.width,
      height: node1.absoluteBoundingBox?.height || node1.height
    };

    const pos2 = {
      x: node2.absoluteBoundingBox?.x || node2.x,
      y: node2.absoluteBoundingBox?.y || node2.y,
      width: node2.absoluteBoundingBox?.width || node2.width,
      height: node2.absoluteBoundingBox?.height || node2.height
    };

    // Calculate vertical spacing
    const verticalSpacing = Math.abs(pos2.y - (pos1.y + pos1.height));

    // Calculate horizontal spacing
    const horizontalSpacing = Math.abs(pos2.x - (pos1.x + pos1.width));

    // Return the smaller spacing value
    return Math.min(verticalSpacing, horizontalSpacing);
  }
}