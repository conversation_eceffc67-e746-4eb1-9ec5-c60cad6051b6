# Color Fallback Feature

## Overview

The Color Fallback Feature automatically extracts colors when text nodes don't have direct fill colors defined. This is particularly useful for widgets like the Dual Color Heading where designers might use selection colors or paint styles instead of direct fills.

## How It Works

When `WidgetUtils.processTextColor()` is called and no fill color is found on a text node, the system automatically tries to extract colors from:

1. **Current Selection Colors** - Colors from currently selected nodes in Figma
2. **Sibling Node Colors** - Colors from sibling nodes in the same parent
3. **Local Paint Styles** - Colors from Figma's local paint styles (future enhancement)

## Implementation Details

### Core Method: `getSelectionColorFallback()`

```typescript
public static getSelectionColorFallback(node: any): string | undefined
```

This method is automatically called by `processTextColor()` when no direct fill is found.

### Fallback Priority

1. **Selection Colors** (Highest Priority)
   - Checks `figma.currentPage.selection`
   - Returns the first solid color found in selected nodes

2. **Sibling Colors** (Medium Priority)
   - Checks sibling nodes in the same parent
   - Returns the first solid color found in siblings

3. **No Color** (Fallback)
   - Returns `undefined` if no colors are found
   - Widget will use default colors

## Usage Examples

### Dual Color Heading Widget

The feature is automatically enabled for dual color heading widgets:

```typescript
// In dualColorHeadingWidgetProcessor.ts
Utils.processTitleNode(node, data, 'eael_dch_first_title', 'eael-dch-first-title');
// This automatically calls WidgetUtils.processTextColor() which includes fallback
```

### Manual Usage

You can also use the fallback method directly:

```typescript
import { WidgetUtils } from '../utils/widgetUtils';

// Get fallback color for a node
const fallbackColor = WidgetUtils.getSelectionColorFallback(textNode);
if (fallbackColor) {
  data.settings.my_color_setting = fallbackColor;
}
```

## Supported Widgets

The color fallback feature works with any widget that uses `WidgetUtils.processTextColor()`:

- ✅ Dual Color Heading
- ✅ CTA Box
- ✅ Info Box
- ✅ Text Editor
- ✅ Heading
- ✅ Counter
- ✅ Advanced Menu
- ✅ Testimonial
- ✅ All other text-based widgets

## Configuration

The feature is enabled by default and requires no configuration. It gracefully handles errors and will silently fall back to no color if any issues occur.

## Benefits

1. **Better Color Detection** - Automatically finds colors even when not directly applied
2. **Designer Friendly** - Works with Figma's selection colors and paint styles
3. **Backward Compatible** - Doesn't break existing functionality
4. **Automatic** - No manual intervention required

## Technical Notes

- The feature only works with `SOLID` color fills
- Gradient colors are not currently supported in fallback
- The method is synchronous for selection and sibling colors
- Local paint styles support is available via `getLocalPaintStyleColor()` (async)

## Future Enhancements

1. **Gradient Support** - Support for gradient colors in fallback
2. **Paint Style Priority** - Make local paint styles higher priority
3. **Color Caching** - Cache frequently used colors for performance
4. **Custom Fallback Rules** - Allow widgets to define custom fallback logic

## Example Scenario

**Before Color Fallback:**
- Text node has no fill → No color applied → Uses default black text

**After Color Fallback:**
- Text node has no fill → Checks selection colors → Finds purple (#CB8FF3) → Applies purple to text

This makes the plugin much more intuitive for designers who use Figma's selection colors panel.
